#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于SMILES的化学品毒性预测与应用域判断整合脚本（包含可视化功能）

功能:
1. 对化学品进行毒性预测（仅基于SMILES）
2. 使用预设的最佳参数判断是否在应用域内
3. 生成分子注意力可视化图
4. 输出整合结果

注意:
- 如果遇到CUDA设备错误，请将配置区域的 USE_CUDA 设置为 False
- 确保系统有足够的GPU内存，或使用CPU模式
"""

import pandas as pd
import numpy as np
import os
import sys
import time
import pickle
import copy

import torch
import torch.nn.functional as F
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem import MACCSkeys
from rdkit.Chem import rdDepictor, MolSurf
from rdkit import DataStructs
try:
    from rdkit.Chem.Draw import rdMolDraw2D, MolToFile, _moltoimg
    HAS_RDKIT_DRAW = True
except ImportError:
    HAS_RDKIT_DRAW = False

import matplotlib.cm as cm
import matplotlib
import warnings
warnings.filterwarnings('ignore')

def setup_cuda_device():
    """智能设置CUDA设备"""
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        if device_count > 1:
            torch.cuda.set_device(1)
            device = torch.device('cuda:1')
            torch.set_default_tensor_type('torch.cuda.FloatTensor')
        else:
            torch.cuda.set_device(0)
            device = torch.device('cuda:0')
            torch.set_default_tensor_type('torch.cuda.FloatTensor')
        return device
    else:
        device = torch.device('cpu')
        torch.set_default_tensor_type('torch.FloatTensor')
        return device

# 设置设备
CURRENT_DEVICE = setup_cuda_device()

def get_safe_cuda_device(use_cuda=True):
    """安全获取CUDA设备"""
    if use_cuda and torch.cuda.is_available():
        return torch.device('cuda:1' if torch.cuda.device_count() > 1 else 'cuda:0')
    return torch.device('cpu')

def set_safe_cuda_device(use_cuda=True):
    """安全设置CUDA设备"""
    if use_cuda and torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        if device_count > 1:
            torch.cuda.set_device(1)
            return torch.device('cuda:1')
        else:
            torch.cuda.set_device(0)
            return torch.device('cuda:0')
    return torch.device('cpu')

# 导入模块
from AFPModel import prepare_smiles_data

try:
    from adsal import NSG
    HAS_ADSAL = True
except ImportError:
    HAS_ADSAL = False
    NSG = None

# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 最佳应用域参数
OPTIMAL_DENSLB = 0.001    # 最佳相似性密度阈值 
OPTIMAL_LDUB = 0.8     # 最佳局域不连续性阈值

# 模型和数据路径（通常不需要修改）
MODEL_PATH = 'saved_model/global_best_model.pt'
TRAINING_DATA_PATH = 'TrainingSet.xlsx'

# 设备配置
USE_CUDA = True  # 是否使用CUDA（推荐，模型在CUDA上训练）
# 注意：如果遇到CUDA设备错误，请将上面的 USE_CUDA 设置为 False

# 可视化配置参数
ENABLE_VISUALIZATION = True  # 是否启用注意力可视化
VISUALIZATION_LIMIT = 10     # 最多可视化多少个分子（避免生成过多PNG文件）
VISUALIZATION_OUTPUT_DIR = 'visualization_outputs'  # 可视化文件输出目录

# 相似性分析配置参数
ENABLE_SIMILARITY_ANALYSIS = True  # 是否启用相似性分析
SIMILARITY_THRESHOLD = 0.3  # 相似性阈值（30%）
SIMILARITY_TOP_N = 5  # 显示前N个最相似的分子
SIMILARITY_OUTPUT_DIR = 'similarity_outputs'  # 相似性分析输出目录

# =============================================================================
# 注意：
# 1. 请确保你已经通过运行AD/AD.py获得了最佳参数
# 2. 将获得的最佳densLB和LdUB值填入上面的OPTIMAL_DENSLB和OPTIMAL_LDUB
# 3. 直接在main函数中修改要分析的SMILES列表
# 4. 运行: python predict_with_AD_visualization.py
# =============================================================================

# =============================================================================
# 可视化功能函数
# =============================================================================

def min_max_norm(dataset):
    """归一化函数"""
    if isinstance(dataset, list):
        norm_list = list()
        min_value = min(dataset)
        max_value = max(dataset)
        for value in dataset:
            tmp = (value - min_value) / (max_value - min_value)
            norm_list.append(tmp)
    return norm_list

# =============================================================================
# 预测功能类和函数
# =============================================================================

class SMILESPredictor:
    """基于SMILES的鱼类急性毒性预测器"""

    def __init__(self, model_path, use_cuda=True):
        """初始化预测器"""
        self.model_path = model_path
        self.use_cuda = use_cuda and torch.cuda.is_available()

        self.device = set_safe_cuda_device(self.use_cuda)

        self._load_model()

    def _load_model(self):
        """加载模型和相关组件"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        # 加载模型到指定设备
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        except TypeError:
            checkpoint = torch.load(self.model_path, map_location=self.device)

        self.feature_dicts = checkpoint['feature_dicts']
        model_config = checkpoint['model_config']

        # 导入模型类
        from AFPModel import SMILESFingerprint
        from AttentiveFP import get_smiles_array

        # 获取特征维度
        sample_smiles = list(self.feature_dicts['smiles_to_atom_mask'].keys())[0]
        x_atom, x_bonds, _, _, _, _ = get_smiles_array([sample_smiles], self.feature_dicts)
        input_feature_dim = x_atom.shape[-1]
        input_bond_dim = x_bonds.shape[-1]

        # 重新构建模型
        self.model = SMILESFingerprint(
            radius=model_config['radius'],
            T=model_config['T'],
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=model_config['fingerprint_dim'],
            output_units_num=model_config['output_units_num'],
            p_dropout=model_config['p_dropout']
        )

        # 加载模型权重
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def predict_single(self, smiles):
        """预测单个SMILES"""
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {'smiles': smiles, 'error': result}

        canonical_smiles = result

        try:
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                [canonical_smiles], self.feature_dicts
            )

            # 转换为tensor并移动到指定设备
            x_atom = torch.Tensor(x_atom).to(self.device)
            x_bonds = torch.Tensor(x_bonds).to(self.device)
            x_atom_index = torch.LongTensor(x_atom_index).to(self.device)
            x_bond_index = torch.LongTensor(x_bond_index).to(self.device)
            x_mask = torch.Tensor(x_mask).to(self.device)

            with torch.no_grad():
                _, mol_prediction = self.model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                y_pred = mol_prediction[:, 0:2]
                y_pred_prob = F.softmax(y_pred, dim=-1).data.cpu().numpy()[0]

                prediction = int(y_pred_prob[1] > 0.5)
                probability = float(y_pred_prob[1])

            result = {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'prediction': prediction,
                'probability': probability,
                'prediction_label': '高毒性' if prediction == 1 else '低毒性',
                'error': None
            }

            return result

        except Exception as e:
            return {'smiles': smiles, 'error': f"预测错误: {str(e)}"}

    def predict_batch(self, smiles_list):
        """批量预测SMILES列表
        
        Args:
            smiles_list: 要预测的SMILES字符串列表
            
        Returns:
            包含预测结果的字典列表，每个字典包含:
            - smiles: 原始SMILES
            - canonical_smiles: 规范化后的SMILES
            - prediction: 预测类别 (0或1)
            - probability: 预测概率 (0-1)
            - prediction_label: 预测标签 ('高毒性'或'低毒性')
            - error: 错误信息 (如果没有错误则为None)
        """
        results = []
        valid_smiles = []
        valid_indices = []
        
        # 第一步: 验证所有SMILES并收集有效SMILES
        for i, smiles in enumerate(smiles_list):
            is_valid, result = self._validate_smiles(smiles)
            if not is_valid:
                results.append({
                    'smiles': smiles,
                    'error': result,
                    'canonical_smiles': None,
                    'prediction': None,
                    'probability': None,
                    'prediction_label': None
                })
            else:
                valid_smiles.append(result)  # 保存规范化后的SMILES
                valid_indices.append(i)
        
        if not valid_smiles:
            return results
            
        # 第二步: 批量预测有效SMILES
        try:
            # 准备批量数据
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                valid_smiles, self.feature_dicts
            )

            # 转换为tensor并移动到指定设备
            x_atom = torch.Tensor(x_atom).to(self.device)
            x_bonds = torch.Tensor(x_bonds).to(self.device)
            x_atom_index = torch.LongTensor(x_atom_index).to(self.device)
            x_bond_index = torch.LongTensor(x_bond_index).to(self.device)
            x_mask = torch.Tensor(x_mask).to(self.device)

            # 预测
            with torch.no_grad():
                _, mol_prediction = self.model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                y_pred = mol_prediction[:, 0:2]
                y_pred_prob = F.softmax(y_pred, dim=-1).data.cpu().numpy()

            # 处理预测结果
            for i, orig_idx in enumerate(valid_indices):
                probability = float(y_pred_prob[i, 1])
                prediction = int(probability > 0.5)
                
                results.append({
                    'smiles': smiles_list[orig_idx],
                    'canonical_smiles': valid_smiles[i],
                    'prediction': prediction,
                    'probability': probability,
                    'prediction_label': '高毒性' if prediction == 1 else '低毒性',
                    'error': None
                })

        except Exception as e:
            # 如果批量预测出错，为所有有效SMILES添加错误信息
            for i, orig_idx in enumerate(valid_indices):
                results.append({
                    'smiles': smiles_list[orig_idx],
                    'error': f"预测错误: {str(e)}",                    'canonical_smiles': valid_smiles[i],
                    'prediction': None,
                    'probability': None,
                    'prediction_label': None
                })
        
        return results


class MolecularVisualization:
    """分子注意力可视化类"""
    
    def __init__(self, model_path, use_cuda=True, output_dir='visualization_outputs'):
        """初始化可视化器"""
        self.model_path = model_path
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.output_dir = output_dir
          # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        self.device = set_safe_cuda_device(self.use_cuda)
            
        self._load_visualization_model()
    
    def _load_visualization_model(self):
        """加载可视化模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        except TypeError:
            checkpoint = torch.load(self.model_path, map_location=self.device)

        self.feature_dicts = checkpoint['feature_dicts']
        model_config = checkpoint['model_config']

        # 获取特征维度
        from AFPModel import prepare_smiles_data
        sample_smiles = list(self.feature_dicts['smiles_to_atom_mask'].keys())[0]
        x_atom, x_bonds, _, _, _, _ = prepare_smiles_data([sample_smiles], self.feature_dicts)
        input_feature_dim = x_atom.shape[-1]
        input_bond_dim = x_bonds.shape[-1]

        # 创建可视化模型
        from AttentiveFP import Fingerprint_viz

        self.radius = model_config['radius']
        self.T = model_config['T']
        self.fingerprint_dim = model_config['fingerprint_dim']
        self.p_dropout = model_config['p_dropout']

        self.model_for_viz = Fingerprint_viz(
            radius=self.radius,
            T=self.T,
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=self.fingerprint_dim,
            output_units_num=self.fingerprint_dim,
            p_dropout=self.p_dropout
        )
        self.model_for_viz = self.model_for_viz.to(self.device)

        # 映射权重
        self._map_weights(checkpoint['model_state_dict'])
        self.model_for_viz.eval()
    
    def _map_weights(self, best_model_wts):
        """将SMILESFingerprint的权重映射到Fingerprint_viz"""
        try:
            src_state = best_model_wts
            dst_state = self.model_for_viz.state_dict()
            adjusted_weights = {}

            for key, value in src_state.items():
                if key.startswith('fingerprint_model.'):
                    new_key = key.replace('fingerprint_model.', '')
                    if new_key in dst_state and dst_state[new_key].shape == value.shape:
                        adjusted_weights[new_key] = value

            # 合并并加载权重
            merged_state = {**dst_state, **adjusted_weights}
            self.model_for_viz.load_state_dict(merged_state, strict=False)

        except Exception:
            pass  # 使用随机初始化的模型
    
    def visualize_molecules(self, smiles_list, compound_names=None, limit=10):
        """对分子列表进行注意力可视化"""
        if not HAS_RDKIT_DRAW:
            print(" RDKit Draw模块不可用，跳过可视化")
            return []
            
        if compound_names is None:
            compound_names = [f"化合物_{i+1}" for i in range(len(smiles_list))]
            
        if limit is not None and len(smiles_list) > limit:
            print(f"限制可视化分子数量为 {limit} 个")
            smiles_list = smiles_list[:limit]
            compound_names = compound_names[:limit]
        
        visualization_results = []
        
        for smiles, compound_name in zip(smiles_list, compound_names):
            try:
                # 验证SMILES
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    continue
                
                # 获取分子特征 - 使用prepare_smiles_data支持动态特征生成
                from AFPModel import prepare_smiles_data
                x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, smiles_to_rdkit_list = prepare_smiles_data([smiles], self.feature_dicts)
                
                # 转换为tensor
                x_atom_tensor = torch.Tensor(x_atom).to(self.device)
                x_bonds_tensor = torch.Tensor(x_bonds).to(self.device)
                x_atom_index_tensor = torch.LongTensor(x_atom_index).to(self.device)
                x_bond_index_tensor = torch.LongTensor(x_bond_index).to(self.device)
                x_mask_tensor = torch.Tensor(x_mask).to(self.device)
                
                # 通过可视化模型获取注意力权重
                with torch.no_grad():
                    atom_feature_viz, _, _, _, \
                    mol_attention_weight_viz, _ = self.model_for_viz(
                        x_atom_tensor, x_bonds_tensor,
                        x_atom_index_tensor, x_bond_index_tensor,
                        x_mask_tensor
                    )
                
                # 提取注意力特征
                atom_feature = np.stack([atom_feature_viz[L].cpu().detach().numpy() for L in range(self.radius+1)])
                atom_weight = np.stack([mol_attention_weight_viz[t].cpu().detach().numpy() for t in range(self.T)])
                
                # 处理单个分子的可视化
                atom_num = 0  # 第一个（也是唯一一个）分子
                ind_mask = x_mask[atom_num]
                ind_atom = smiles_to_rdkit_list[smiles]
                ind_feature = atom_feature[:, atom_num]
                ind_weight = atom_weight[:, atom_num]
                
                out_feature = []
                out_weight = []
                for j, one_or_zero in enumerate(list(ind_mask)):
                    if one_or_zero == 1.0:
                        out_feature.append(ind_feature[:,j])
                        out_weight.append(ind_weight[:,j])
                  # 生成可视化
                png_filename = self._generate_attention_visualization(
                    mol, smiles, compound_name, out_weight, ind_atom
                )
                
                if png_filename:
                    visualization_results.append({
                        'compound_name': compound_name,
                        'smiles': smiles,
                        'png_file': png_filename                })
                
            except Exception as e:
                print(f"分子 {compound_name} 可视化失败: {e}")
                continue
        
        return visualization_results
    
    def _generate_attention_visualization(self, mol, smiles, compound_name, out_weight, ind_atom):
        """生成注意力可视化PNG文件"""
        try:
            # 使用真实的注意力权重进行可视化
            weight_norm = min_max_norm([out_weight[m][0] for m in np.argsort(ind_atom)])
            
            # 设置可视化参数
            norm = matplotlib.colors.Normalize(vmin=0, vmax=1)
            try:
                cmap = matplotlib.colormaps.get_cmap('Oranges')
            except AttributeError:
                cmap = cm.get_cmap('Oranges')
            plt_colors = cm.ScalarMappable(norm=norm, cmap=cmap)
            atom_colors = {}
            
            weight_norm = np.array(weight_norm).flatten()
            
            # 对于小分子，动态调整阈值
            if len(weight_norm) > 3:
                threshold_count = min(6, len(weight_norm)//2)
                threshold = weight_norm[np.argsort(weight_norm)[-threshold_count]]
                weight_norm = np.where(weight_norm < threshold, 0, weight_norm)
            
            # 为每个原子设置颜色
            for k in range(len(ind_atom)):
                atom_colors[k] = plt_colors.to_rgba(float(weight_norm[k]))
            
            # 生成分子2D结构
            rdDepictor.Compute2DCoords(mol)
            
            # 创建PNG绘制器（更高分辨率）
            drawer = rdMolDraw2D.MolDraw2DCairo(400, 400)
            drawer.SetFontSize(12)
            
            # 绘制分子
            mol = rdMolDraw2D.PrepareMolForDrawing(mol)
            drawer.DrawMolecule(mol, 
                               highlightAtoms=range(0, len(ind_atom)),
                               highlightBonds=[],
                               highlightAtomColors=atom_colors)
            drawer.FinishDrawing()
            
            # 清理文件名中的特殊字符
            safe_compound_name = "".join(c for c in compound_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_smiles = "".join(c for c in smiles[:10] if c.isalnum() or c in ('-', '_'))
            png_filename = os.path.join(self.output_dir, f"attention_{safe_compound_name}_{safe_smiles}.png")
              # 获取PNG数据并保存到文件
            png_data = drawer.GetDrawingText()
            with open(png_filename, 'wb') as f:
                f.write(png_data)
            return png_filename

        except Exception:
            return None


class SimilarityAnalysis:
    """分子相似性分析类"""
    
    def __init__(self, training_data_path, model_path, use_cuda=True, output_dir='similarity_outputs'):
        """初始化相似性分析器"""
        self.training_data_path = training_data_path
        self.model_path = model_path
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.output_dir = output_dir
        
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        self.device = set_safe_cuda_device(self.use_cuda)
        
        # 加载训练数据
        self._load_training_data()
        
        # 初始化可视化器
        self.visualizer = None
        
    def _load_training_data(self):
        """加载训练数据"""
        if not os.path.exists(self.training_data_path):
            raise FileNotFoundError(f"训练数据文件不存在: {self.training_data_path}")

        self.training_df = pd.read_excel(self.training_data_path)

        # 检查必要列
        required_cols = ['smiles']
        missing_cols = [col for col in required_cols if col not in self.training_df.columns]
        if missing_cols:
            raise ValueError(f"训练集缺少必要列: {missing_cols}")

        # 预计算训练集分子指纹
        self._precompute_fingerprints()
    
    def _precompute_fingerprints(self):
        """预计算训练集分子指纹"""
        self.training_fps = []
        self.valid_training_indices = []

        for idx, row in self.training_df.iterrows():
            smiles = row['smiles']
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    # 使用Morgan指纹（2048位，半径2）
                    # fp = AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
                    fp = MACCSkeys.GenMACCSKeys(mol)
                    self.training_fps.append(fp)
                    self.valid_training_indices.append(idx)
            except Exception:
                continue
    
    def find_similar_molecules(self, query_smiles, threshold=0.3, top_n=5):
        """查找与查询分子相似的训练集分子"""
        # 计算查询分子指纹
        try:
            query_mol = Chem.MolFromSmiles(query_smiles)
            if query_mol is None:
                raise ValueError(f"无法解析查询SMILES: {query_smiles}")

            # 使用MACCS指纹保持一致性
            query_fp = MACCSkeys.GenMACCSKeys(query_mol)
        except Exception as e:
            raise ValueError(f"查询分子处理失败: {e}")

        # 计算相似性
        similarities = []
        for i, training_fp in enumerate(self.training_fps):
            similarity = DataStructs.TanimotoSimilarity(query_fp, training_fp)
            if similarity >= threshold:
                training_idx = self.valid_training_indices[i]
                training_row = self.training_df.iloc[training_idx]
                similarities.append({
                    'training_index': training_idx,
                    'smiles': training_row['smiles'],
                    'similarity': similarity,
                    'label': training_row.get('y', 'N/A'),
                    'source': training_row.get('source', training_row.get('Source', 'N/A'))
                })

        # 按相似性排序并返回前top_n个
        similarities.sort(key=lambda x: x['similarity'], reverse=True)

        return {
            'query_smiles': query_smiles,
            'total_similar': len(similarities),
            'threshold': threshold,
            'top_similarities': similarities[:top_n]
        }
    
    def visualize_similar_molecules(self, similarity_result, compound_name="查询化合物"):
        """对相似分子进行注意力可视化"""
        if not ENABLE_VISUALIZATION or not HAS_RDKIT_DRAW:
            return []

        # 初始化可视化器
        if self.visualizer is None:
            self.visualizer = MolecularVisualization(
                self.model_path,
                use_cuda=self.use_cuda,
                output_dir=self.output_dir
            )

        # 准备可视化数据
        smiles_to_visualize = [similarity_result['query_smiles']]
        compound_names = [f"{compound_name}_查询分子"]

        # 添加相似分子
        for i, sim_info in enumerate(similarity_result['top_similarities']):
            smiles_to_visualize.append(sim_info['smiles'])
            compound_names.append(f"{compound_name}_相似分子{i+1}_sim{sim_info['similarity']:.3f}")

        # 执行可视化
        return self.visualizer.visualize_molecules(
            smiles_to_visualize,
            compound_names,
            limit=len(smiles_to_visualize)
        )
    
    def generate_similarity_report(self, similarity_result, compound_name="查询化合物"):
        """生成相似性分析报告"""
        # 保存详细报告到文件
        report_filename = os.path.join(self.output_dir, f"similarity_report_{compound_name}.txt")
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(f"相似性分析报告 - {compound_name}\n")
            f.write(f"{'='*60}\n\n")
            f.write(f"查询分子SMILES: {similarity_result['query_smiles']}\n")
            f.write(f"相似性阈值: {similarity_result['threshold']:.1%}\n")
            f.write(f"总相似分子数: {similarity_result['total_similar']}\n")
            f.write(f"展示前 {len(similarity_result['top_similarities'])} 个最相似分子\n\n")

            f.write("最相似分子详情:\n")
            f.write(f"{'='*60}\n")
            for i, sim_info in enumerate(similarity_result['top_similarities']):
                f.write(f"\n第 {i+1} 名:\n")
                f.write(f"  SMILES: {sim_info['smiles']}\n")
                f.write(f"  相似性: {sim_info['similarity']:.4f} ({sim_info['similarity']:.1%})\n")
                f.write(f"  训练集标签: {sim_info['label']}\n")
                f.write(f"  训练集索引: {sim_info['training_index']}\n")

        return report_filename


def predict_batch_with_AD_visualization(smiles_list, model_path=MODEL_PATH, training_data_path=TRAINING_DATA_PATH,
                                      optimal_densLB=OPTIMAL_DENSLB, optimal_LdUB=OPTIMAL_LDUB):
    """
    对SMILES列表进行毒性预测、应用域判断、可视化和相似性分析
    
    Args:
        smiles_list: 要预测的SMILES字符串列表
        model_path: 模型文件路径
        training_data_path: 训练数据路径
        optimal_densLB: 最佳相似性密度阈值
        optimal_LdUB: 最佳局域不连续性阈值
        
    Returns:
        包含预测结果和应用域判断的DataFrame
    """
    # 1. 初始化预测器
    predictor = SMILESPredictor(model_path, use_cuda=USE_CUDA)
    
    # 2. 批量预测
    prediction_results = predictor.predict_batch(smiles_list)
    
    # 转换为DataFrame
    df_pred = pd.DataFrame(prediction_results)
    
    # 过滤掉有错误的行
    df_valid = df_pred[df_pred['error'].isna()].copy()
    
    if len(df_valid) == 0:
        return df_pred, [], []
      # 3. 加载训练数据和应用域判断
    if HAS_ADSAL:
        df_train = load_training_data(training_data_path)
        df_with_metrics = calculate_ad_metrics(df_train, df_valid)
        df_final = apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB)
        
        # 合并回原始结果
        df_pred = df_pred.merge(df_final, on='smiles', how='left', suffixes=('', '_y'))
    else:
        # 为所有行添加应用域相关列（设置默认值）
        df_pred['in_applicability_domain'] = True  # 假设都在应用域内
        df_pred['ad_reason'] = "应用域判断模块不可用"
        df_pred['ad_density_value'] = 0.0
        df_pred['ad_ld_value'] = 0.0
        df_pred['ad_densLB_threshold'] = optimal_densLB
        df_pred['ad_LdUB_threshold'] = optimal_LdUB
    
    # 4. 运行可视化
    visualization_results = run_visualization(df_pred)
    
    # 5. 运行相似性分析
    similarity_results = run_similarity_analysis(df_pred)
    return df_pred, visualization_results, similarity_results

# exp权重函数
def expWt(x, a=10, eps=1e-6):
    """指数权重函数"""
    return np.exp(-a*(1-x)/(x + eps))

EXP_WEIGHT_PARAMS = {'a': 10}

# =============================================================================
# 核心功能函数
# =============================================================================

def load_training_data(file_path):
    """加载训练集数据"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")

    df = pd.read_excel(file_path)

    # 检查必要列
    required_cols = ['smiles', 'y']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 准备数据
    df_clean = df[['smiles', 'y']].copy()
    df_clean.reset_index(drop=True, inplace=True)

    return df_clean

def calculate_ad_metrics(df_train, df_query):
    """计算应用域指标"""
    if not HAS_ADSAL:
        raise ImportError("adsal模块不可用，无法计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 合并结果
    return df_query.join(ad_metrics)

def apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB):
    """应用应用域判断标准"""
    # 应用域判断条件
    ad_condition = (
        (df_with_metrics['simiDensity|exp'] >= optimal_densLB) &
        (df_with_metrics['simiWtLD_w|exp'] <= optimal_LdUB)
    )

    # 添加应用域判断结果
    df_result = df_with_metrics.copy()
    df_result['in_applicability_domain'] = ad_condition
    df_result['ad_densLB_threshold'] = optimal_densLB
    df_result['ad_LdUB_threshold'] = optimal_LdUB
    df_result['ad_density_value'] = df_with_metrics['simiDensity|exp']
    df_result['ad_ld_value'] = df_with_metrics['simiWtLD_w|exp']

    # 添加应用域判断原因
    def get_ad_reason(row):
        if row['in_applicability_domain']:
            return "在应用域内"
        else:
            reasons = []
            if row['ad_density_value'] < optimal_densLB:
                reasons.append(f"相似性密度({row['ad_density_value']:.3f}) < 阈值({optimal_densLB})")
            if row['ad_ld_value'] > optimal_LdUB:
                reasons.append(f"局域不连续性({row['ad_ld_value']:.3f}) > 阈值({optimal_LdUB})")
            return "; ".join(reasons)

    df_result['ad_reason'] = df_result.apply(get_ad_reason, axis=1)
    return df_result

def run_visualization(df_result):
    """运行可视化"""
    if not ENABLE_VISUALIZATION:
        return []

    try:
        # 创建可视化器
        visualizer = MolecularVisualization(
            MODEL_PATH,
            use_cuda=USE_CUDA,
            output_dir=VISUALIZATION_OUTPUT_DIR
        )

        # 准备可视化数据
        smiles_list = df_result['canonical_smiles'].tolist()
        if 'compound_name' in df_result.columns:
            compound_names = df_result['compound_name'].tolist()
        else:
            compound_names = [f"化合物_{i+1}" for i in range(len(smiles_list))]

        # 执行可视化
        return visualizer.visualize_molecules(
            smiles_list,
            compound_names,
            limit=VISUALIZATION_LIMIT
        )

    except Exception:
        return []

def run_similarity_analysis(df_result):
    """运行相似性分析"""
    if not ENABLE_SIMILARITY_ANALYSIS:
        print("相似性分析已禁用")
        return []

    try:
        print("开始相似性分析...")
        # 创建相似性分析器
        similarity_analyzer = SimilarityAnalysis(
            TRAINING_DATA_PATH,
            MODEL_PATH,
            use_cuda=USE_CUDA,
            output_dir=SIMILARITY_OUTPUT_DIR
        )

        similarity_results = []

        # 对每个查询分子进行相似性分析
        for idx, row in df_result.iterrows():
            # 检查是否有有效的SMILES
            if pd.isna(row.get('canonical_smiles')) or row.get('error') is not None:
                print(f"跳过化合物 {idx+1}: 无有效SMILES或存在错误")
                continue
                
            smiles = row['canonical_smiles']
            compound_name = row.get('compound_name', f'化合物_{idx+1}')
            print(f"分析化合物: {compound_name} ({smiles})")

            # 查找相似分子
            similarity_result = similarity_analyzer.find_similar_molecules(
                smiles,
                threshold=SIMILARITY_THRESHOLD,
                top_n=SIMILARITY_TOP_N
            )

            # 生成报告
            report_file = similarity_analyzer.generate_similarity_report(
                similarity_result,
                compound_name
            )
            print(f"生成报告: {report_file}")

            # 可视化相似分子
            viz_results = similarity_analyzer.visualize_similar_molecules(
                similarity_result,
                compound_name
            )

            similarity_results.append({
                'compound_name': compound_name,
                'similarity_result': similarity_result,
                'report_file': report_file,
                'visualization_results': viz_results
            })

        print(f"相似性分析完成，共处理 {len(similarity_results)} 个化合物")
        return similarity_results

    except Exception as e:
        print(f"相似性分析出错: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return []

def generate_summary(df_result, visualization_results=None, similarity_results=None):
    """生成结果摘要"""
    print("\n结果摘要:")
    print("=" * 50)

    for idx, row in df_result.iterrows():
        compound_name = row.get('compound_name', f'化合物_{idx+1}')
          # 安全获取应用域状态
        in_domain = row.get('in_applicability_domain', True)
        status = "应用域内" if in_domain else "应用域外"

        print(f"\n{compound_name}:")
        print(f"  SMILES: {row['smiles']}")
        print(f"  毒性预测: {row.get('prediction_label', 'N/A')}")
        
        # 安全获取预测概率
        probability = row.get('probability')
        if probability is not None:
            print(f"  预测概率: {probability:.3f}")
        else:
            print(f"  预测概率: N/A")
            
        print(f"  应用域状态: {status}")
        
        # 安全获取应用域指标
        density_value = row.get('ad_density_value', 0.0)
        ld_value = row.get('ad_ld_value', 0.0)
        ad_reason = row.get('ad_reason', 'N/A')
        
        print(f"  相似性密度: {density_value:.4f}")
        print(f"  局域不连续性: {ld_value:.4f}")
        print(f"  判断原因: {ad_reason}")# 添加可视化信息
        if visualization_results:
            viz_info = next((v for v in visualization_results if v['compound_name'] == compound_name), None)
            if viz_info:
                print(f"  可视化文件: {viz_info['png_file']}")

        # 添加相似性分析信息
        if similarity_results:
            sim_info = next((s for s in similarity_results if s['compound_name'] == compound_name), None)
            if sim_info:
                sim_result = sim_info['similarity_result']
                print(f"  相似分子数量: {sim_result['total_similar']} (阈值≥{sim_result['threshold']:.1%})")
                if sim_result['top_similarities']:
                    top_sim = sim_result['top_similarities'][0]
                    print(f"  最相似分子: {top_sim['similarity']:.3f} ({top_sim['similarity']:.1%})")
                print(f"  相似性报告: {sim_info['report_file']}")
                if sim_info['visualization_results']:
                    print(f"  相似分子可视化: {len(sim_info['visualization_results'])} 个文件")        # 给出建议
        if row.get('in_applicability_domain', True):
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"  建议: {suggestion}")

    print("=" * 50)

# Legacy function removed - functionality integrated into predict_batch_with_AD_visualization

def run_analysis(smiles_list, compound_names=None, output_file=None,
                 model_path=MODEL_PATH, training_data_path=TRAINING_DATA_PATH,
                 optimal_densLB=OPTIMAL_DENSLB, optimal_LdUB=OPTIMAL_LDUB,
                 enable_visualization=ENABLE_VISUALIZATION, 
                 enable_similarity_analysis=ENABLE_SIMILARITY_ANALYSIS):
    """
    运行化学品毒性预测与应用域判断整合分析（包含可视化）
    
    Args:
        smiles_list: 要分析的SMILES字符串列表
        compound_names: 化合物名称列表（可选，用于文件命名）
        output_file: 输出Excel文件名（可选）
        model_path: 模型文件路径
        training_data_path: 训练数据路径
        optimal_densLB: 最佳相似性密度阈值
        optimal_LdUB: 最佳局域不连续性阈值
        enable_visualization: 是否启用可视化
        enable_similarity_analysis: 是否启用相似性分析
        
    Returns:
        tuple: (结果DataFrame, 可视化结果列表, 相似性分析结果列表)
    """
    # 分析开始

    # 临时设置全局变量（为了兼容现有函数）
    global ENABLE_VISUALIZATION, ENABLE_SIMILARITY_ANALYSIS
    original_viz = ENABLE_VISUALIZATION
    original_sim = ENABLE_SIMILARITY_ANALYSIS
    ENABLE_VISUALIZATION = enable_visualization
    ENABLE_SIMILARITY_ANALYSIS = enable_similarity_analysis

    try:
        # 步骤1: 执行批量预测和分析
        df_final, visualization_results, similarity_results = predict_batch_with_AD_visualization(
            smiles_list, 
            model_path=model_path,
            training_data_path=training_data_path,
            optimal_densLB=optimal_densLB,
            optimal_LdUB=optimal_LdUB
        )
        
        # 添加化合物名称到结果中（如果提供）
        if compound_names and len(compound_names) == len(smiles_list):
            df_final['compound_name'] = compound_names

        # 步骤2: 保存结果（如果指定了输出文件）
        if output_file:
            df_final.to_excel(output_file, index=False)

        # 步骤3: 生成摘要
        generate_summary(df_final, visualization_results, similarity_results)
        
        return df_final, visualization_results, similarity_results

    except Exception as e:
        raise e
    finally:
        # 恢复全局变量
        ENABLE_VISUALIZATION = original_viz
        ENABLE_SIMILARITY_ANALYSIS = original_sim

def run_similarity_analysis_only(test_smiles, compound_name=None):
    """独立运行相似性分析（不依赖模型预测）"""
    if compound_name is None:
        compound_name = f"化合物_{test_smiles[:10]}"
    
    print(f"分析化合物: {compound_name} ({test_smiles})")
    
    try:
        from rdkit import Chem
        from rdkit.Chem import MACCSkeys
        from rdkit import DataStructs
        
        # 1. 检查训练数据
        if not os.path.exists(TRAINING_DATA_PATH):
            print(f"训练数据文件不存在: {TRAINING_DATA_PATH}")
            return
        
        # 2. 加载训练数据
        print("加载训练数据...")
        training_df = pd.read_excel(TRAINING_DATA_PATH)
        print(f"训练数据: {len(training_df)} 条记录")
        
        # 3. 预计算训练集指纹
        print("计算训练集分子指纹...")
        training_fps = []
        valid_training_indices = []
        
        for idx, row in training_df.iterrows():
            smiles = row['smiles']
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    fp = MACCSkeys.GenMACCSKeys(mol)
                    training_fps.append(fp)
                    valid_training_indices.append(idx)
            except Exception:
                continue
        
        print(f"有效训练分子: {len(training_fps)} 个")
        
        # 4. 计算查询分子指纹
        print("计算查询分子指纹...")
        query_mol = Chem.MolFromSmiles(test_smiles)
        if query_mol is None:
            print(f"无法解析查询SMILES: {test_smiles}")
            return
        
        query_fp = MACCSkeys.GenMACCSKeys(query_mol)
        
        # 5. 计算相似性
        print("计算相似性...")
        similarities = []
        for i, training_fp in enumerate(training_fps):
            similarity = DataStructs.TanimotoSimilarity(query_fp, training_fp)
            if similarity >= SIMILARITY_THRESHOLD:
                training_idx = valid_training_indices[i]
                training_row = training_df.iloc[training_idx]
                similarities.append({
                    'training_index': training_idx,
                    'smiles': training_row['smiles'],
                    'similarity': similarity,
                    'label': training_row.get('y', 'N/A'),
                    'source': training_row.get('source', training_row.get('Source', 'N/A'))
                })
        
        # 6. 排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        
        print(f"找到 {len(similarities)} 个相似分子 (阈值≥{SIMILARITY_THRESHOLD:.1%})")
        
        # 7. 显示前几个最相似的分子
        if similarities:
            print(f"前 {min(SIMILARITY_TOP_N, len(similarities))} 个最相似分子:")
            for i, sim_info in enumerate(similarities[:SIMILARITY_TOP_N]):
                print(f"  {i+1}. SMILES: {sim_info['smiles']}")
                print(f"     相似性: {sim_info['similarity']:.4f} ({sim_info['similarity']:.1%})")
                print(f"     标签: {sim_info['label']}")
                print(f"     训练集索引: {sim_info['training_index']}")
                print()
        
        # 8. 创建输出目录
        if not os.path.exists(SIMILARITY_OUTPUT_DIR):
            os.makedirs(SIMILARITY_OUTPUT_DIR)
        
        # 9. 生成报告文件
        report_filename = os.path.join(SIMILARITY_OUTPUT_DIR, f"similarity_report_{compound_name}.txt")
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(f"相似性分析报告 - {compound_name}\n")
            f.write(f"{'='*60}\n")
            f.write(f"查询分子SMILES: {test_smiles}\n")
            f.write(f"相似性阈值: {SIMILARITY_THRESHOLD:.1%}\n")
            f.write(f"总相似分子数: {len(similarities)}\n")
            f.write(f"展示前 {SIMILARITY_TOP_N} 个最相似分子\n\n")
            f.write("最相似分子详情:")
            f.write(f"{'='*60}\n")
            for i, sim_info in enumerate(similarities[:SIMILARITY_TOP_N]):
                f.write(f"第 {i+1} 名:")
                f.write(f"  SMILES: {sim_info['smiles']}\n")
                f.write(f"  相似性: {sim_info['similarity']:.4f} ({sim_info['similarity']:.1%})\n")
                f.write(f"  训练集标签: {sim_info['label']}\n")
                f.write(f"  训练集索引: {sim_info['training_index']}\n")
        
        print(f"报告已保存到: {report_filename}")
        print("相似性分析完成!")
        
    except Exception as e:
        print(f"相似性分析出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数 - 仅用于独立运行时的示例"""
    # 示例化合物SMILES列表 - 请在这里修改要分析的化合物
    test_smiles = [
        'CCO',  # 乙醇
    ]
    
    # 可选：为化合物添加名称（用于报告和可视化）
    compound_names = [
        'Ethanol',
    ]
    
    # 选择运行模式：
    # 模式1：完整分析（需要完整环境）
    try:
        df_result, viz_results, sim_results = run_analysis(
            smiles_list=test_smiles,
            compound_names=compound_names,
            output_file='prediction_with_AD_visualization_results.xlsx'
        )
        return df_result, viz_results, sim_results
    except Exception as e:
        print(f"完整分析失败: {e}")
        print("切换到仅相似性分析模式...")
        
        # 模式2：仅相似性分析（轻量级）
        for smiles, name in zip(test_smiles, compound_names):
            run_similarity_analysis_only(smiles, name)
        
        return None, [], []

if __name__ == "__main__":
    main()
