"""
基于SMILES的鱼类急性毒性预测模型
只使用AttentiveFP处理分子结构特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from AttentiveFP import Fingerprint
import numpy as np

class SMILESFingerprint(nn.Module):
    """
    基于SMILES的单模态模型，只使用AttentiveFP处理分子结构
    """
    def __init__(self, radius, T, input_feature_dim, input_bond_dim,
                 fingerprint_dim, output_units_num, p_dropout=0.1):
        super(SMILESFingerprint, self).__init__()

        # AttentiveFP模块处理SMILES
        self.fingerprint_model = Fingerprint(
            radius=radius,
            T=T,
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=fingerprint_dim,
            output_units_num=fingerprint_dim,  # 输出指纹维度而不是最终预测
            p_dropout=p_dropout
        )
        
        # 预测层
        self.prediction_layer = nn.Sequential(
            nn.Linear(fingerprint_dim, fingerprint_dim // 2),
            nn.ReLU(),
            nn.Dropout(p_dropout),
            nn.Linear(fingerprint_dim // 2, fingerprint_dim // 4),
            nn.ReLU(),
            nn.Dropout(p_dropout),
            nn.Linear(fingerprint_dim // 4, output_units_num)
        )
        
    def forward(self, x_atom, x_bonds, x_atom_index, x_bond_index, x_mask):
        # 通过AttentiveFP获取分子指纹
        atoms_prediction, mol_fingerprint = self.fingerprint_model(
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
        )
        
        # 最终预测
        output = self.prediction_layer(mol_fingerprint)
        
        return atoms_prediction, output

def prepare_smiles_data(smiles_list, feature_dicts):
    """
    准备SMILES数据
    支持动态生成新SMILES的特征
    """
    from AttentiveFP import get_smiles_array
    from AttentiveFP.getFeatures_aromaticity_rm import gen_descriptor_data, get_smiles_dicts
    import numpy as np
    
    # 检查是否有新的SMILES需要处理
    new_smiles = [s for s in smiles_list if s not in feature_dicts['smiles_to_atom_mask']]
    
    if new_smiles:
        # 为新SMILES生成特征
        try:
            # 生成新SMILES的特征
            new_feature_dicts = get_smiles_dicts(new_smiles)

            # 合并到现有特征字典中
            for key in feature_dicts.keys():
                if key in new_feature_dicts:
                    feature_dicts[key].update(new_feature_dicts[key])

        except Exception as e:
            # 如果特征生成失败，从列表中移除这些SMILES
            failed_smiles = [s for s in new_smiles if s not in feature_dicts['smiles_to_atom_mask']]
            if failed_smiles:
                raise ValueError(f"无法为以下SMILES生成特征: {failed_smiles}")
    
    # 处理SMILES数据
    try:
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, smiles_to_rdkit_list = get_smiles_array(
            smiles_list, feature_dicts
        )
        return (x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, smiles_to_rdkit_list)
    except Exception as e:
        failed_smiles = [s for s in smiles_list if s not in feature_dicts['smiles_to_atom_mask']]
        raise ValueError(f"SMILES处理失败: {failed_smiles}") from e

def train_smiles_model(model, dataset, optimizer, loss_function, feature_dicts, tasks,
                      per_task_output_units_num, batch_size, epoch):
    """
    SMILES模型训练函数
    """
    model.train()
    np.random.seed(epoch)
    valList = np.arange(0, dataset.shape[0])
    np.random.shuffle(valList)

    batch_list = []
    for i in range(0, dataset.shape[0], batch_size):
        batch = valList[i:i+batch_size]
        batch_list.append(batch)

    for train_batch in batch_list:
        batch_df = dataset.loc[train_batch, :].reset_index(drop=True)
        smiles_list = batch_df.smiles.values

        # 准备SMILES数据
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
            smiles_list, feature_dicts
        )

        # 转换为tensor
        x_atom = torch.Tensor(x_atom)
        x_bonds = torch.Tensor(x_bonds)
        x_atom_index = torch.cuda.LongTensor(x_atom_index) if torch.cuda.is_available() else torch.LongTensor(x_atom_index)
        x_bond_index = torch.cuda.LongTensor(x_bond_index) if torch.cuda.is_available() else torch.LongTensor(x_bond_index)
        x_mask = torch.Tensor(x_mask)
        
        # 前向传播
        _, mol_prediction = model(
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
        )
        
        optimizer.zero_grad()
        loss = 0.0
        
        for i, task in enumerate(tasks):
            y_pred = mol_prediction[:, i * per_task_output_units_num:(i + 1) * per_task_output_units_num]
            y_val = batch_df[task].values
            
            validInds = np.where((y_val == 0) | (y_val == 1))[0]
            if len(validInds) == 0:
                continue
                
            y_val_adjust = np.array([y_val[v] for v in validInds]).astype(float)
            validInds = torch.cuda.LongTensor(validInds).squeeze() if torch.cuda.is_available() else torch.LongTensor(validInds).squeeze()
            y_pred_adjust = torch.index_select(y_pred, 0, validInds)

            y_val_tensor = torch.cuda.LongTensor(y_val_adjust) if torch.cuda.is_available() else torch.LongTensor(y_val_adjust)
            loss += loss_function[i](y_pred_adjust, y_val_tensor)
        
        loss.backward()
        optimizer.step()

def eval_smiles_model(model, dataset, loss_function, feature_dicts, tasks, 
                     per_task_output_units_num, batch_size):
    """
    SMILES模型评估函数
    """
    model.eval()
    y_val_list = {}
    y_pred_list = {}
    losses_list = []
    valList = np.arange(0, dataset.shape[0])
    
    batch_list = []
    for i in range(len(tasks)):
        y_val_list[i] = []
        y_pred_list[i] = []
    
    for i in range(0, dataset.shape[0], batch_size):
        batch = valList[i:i+batch_size]
        batch_list.append(batch)
    
    for eval_batch in batch_list:
        batch_df = dataset.loc[eval_batch, :].reset_index(drop=True)
        smiles_list = batch_df.smiles.values
        
        # 准备SMILES数据
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
            smiles_list, feature_dicts
        )
        
        # 转换为tensor
        x_atom = torch.Tensor(x_atom)
        x_bonds = torch.Tensor(x_bonds)
        x_atom_index = torch.cuda.LongTensor(x_atom_index) if torch.cuda.is_available() else torch.LongTensor(x_atom_index)
        x_bond_index = torch.cuda.LongTensor(x_bond_index) if torch.cuda.is_available() else torch.LongTensor(x_bond_index)
        x_mask = torch.Tensor(x_mask)
        
        # 前向传播
        _, mol_prediction = model(
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
        )
        
        for i, task in enumerate(tasks):
            y_pred = mol_prediction[:, i * per_task_output_units_num:(i + 1) * per_task_output_units_num]
            y_val = batch_df[task].values
            
            validInds = np.where((y_val == 0) | (y_val == 1))[0]
            if len(validInds) == 0:
                continue
                
            y_val_adjust = np.array([y_val[v] for v in validInds]).astype(float)
            validInds = torch.cuda.LongTensor(validInds).squeeze() if torch.cuda.is_available() else torch.LongTensor(validInds).squeeze()
            y_pred_adjust = torch.index_select(y_pred, 0, validInds)

            y_val_tensor = torch.cuda.LongTensor(y_val_adjust) if torch.cuda.is_available() else torch.LongTensor(y_val_adjust)
            loss = loss_function[i](y_pred_adjust, y_val_tensor)
            y_pred_adjust = F.softmax(y_pred_adjust, dim=-1).data.cpu().numpy()[:, 1]
            losses_list.append(loss.cpu().detach().numpy())
            
            y_val_list[i].extend(y_val_adjust)
            y_pred_list[i].extend(y_pred_adjust)
    
    from sklearn.metrics import roc_auc_score
    eval_roc = [roc_auc_score(y_val_list[i], y_pred_list[i]) for i in range(len(tasks))]
    eval_loss = np.array(losses_list).mean()
    
    return eval_roc, eval_loss
