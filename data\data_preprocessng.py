import pandas as pd
from sklearn.model_selection import train_test_split
import numpy as np

def split_dataset_by_smiles(input_file_path, smiles_column='smiles', random_state=42, output_file_path=None):
    """
    根据SMILES列将Excel数据按照8:1:1划分为训练集、验证集和测试集
    
    参数:
    input_file_path: 输入Excel文件路径
    smiles_column: SMILES列名，默认为'smiles'
    random_state: 随机种子，默认为42
    output_file_path: 输出文件路径，默认为None时自动生成
    
    返回:
    输出文件路径
    """
    
    # 读取Excel文件
    print(f"正在读取文件: {input_file_path}")
    df = pd.read_excel(input_file_path)
    
    # 检查SMILES列是否存在
    if smiles_column not in df.columns:
        available_columns = ', '.join(df.columns.tolist())
        raise ValueError(f"未找到列 '{smiles_column}'。可用列名: {available_columns}")
    
    print(f"数据总量: {len(df)} 条")
    print(f"SMILES列名: {smiles_column}")
    
    # 设置随机种子
    np.random.seed(random_state)
    
    # 创建索引数组
    indices = np.arange(len(df))
    
    # 第一次划分：80% 训练集，20% 临时集（验证集+测试集）
    train_indices, temp_indices = train_test_split(
        indices, 
        test_size=0.2, 
        random_state=random_state,
        shuffle=True
    )
    
    # 第二次划分：将临时集按1:1划分为验证集和测试集
    valid_indices, test_indices = train_test_split(
        temp_indices,
        test_size=0.5,
        random_state=random_state,
        shuffle=True
    )
    
    # 创建副本以避免修改原数据
    result_df = df.copy()
    
    # 添加set列
    result_df['set'] = ''
    result_df.loc[train_indices, 'set'] = 'train'
    result_df.loc[valid_indices, 'set'] = 'valid'
    result_df.loc[test_indices, 'set'] = 'test'
    
    # 统计各集合大小
    train_count = len(train_indices)
    valid_count = len(valid_indices)
    test_count = len(test_indices)
    
    print(f"\n数据集划分结果:")
    print(f"训练集 (train): {train_count} 条 ({train_count/len(df)*100:.1f}%)")
    print(f"验证集 (valid): {valid_count} 条 ({valid_count/len(df)*100:.1f}%)")
    print(f"测试集 (test): {test_count} 条 ({test_count/len(df)*100:.1f}%)")
    
    # 生成输出文件路径
    if output_file_path is None:
        if input_file_path.endswith('.xlsx'):
            output_file_path = input_file_path.replace('.xlsx', '_with_split.xlsx')
        elif input_file_path.endswith('.xls'):
            output_file_path = input_file_path.replace('.xls', '_with_split.xlsx')
        else:
            output_file_path = input_file_path + '_with_split.xlsx'
    
    # 保存结果
    print(f"\n正在保存到: {output_file_path}")
    result_df.to_excel(output_file_path, index=False)
    print("保存完成！")
    
    return output_file_path

# 使用示例
if __name__ == "__main__":
    # 使用示例
    input_file = "prepro_FishLC50_scr.xlsx"  # 替换为你的输入文件路径
    
    try:
        # 调用函数进行数据集划分
        output_file = split_dataset_by_smiles(
            input_file_path=input_file,
            smiles_column='Canonical smiles',  # 根据实际列名调整
            random_state=42,  # 固定随机种子
            output_file_path=None  # 自动生成输出文件名
        )
        
        print(f"\n数据集划分完成！输出文件: {output_file}")
        
        # 验证结果
        result_df = pd.read_excel(output_file)
        print(f"\n验证结果:")
        print(result_df['set'].value_counts())
        print(f"\n前5行预览:")
        print(result_df.head())
        
    except Exception as e:
        print(f"错误: {e}")